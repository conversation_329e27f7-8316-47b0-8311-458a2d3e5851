package com.sayweee.datasync.fetcher.jira.builder;

import com.sayweee.datasync.model.request.JiraSyncRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.stream.Collectors;

@Slf4j
@Component
public class JqlQueryBuilder {
    private static final String DEFAULT_ORDER = " ORDER BY updated DESC";

    public String buildJql(JiraSyncRequest request) {
        log.debug("Building JQL. Request: {}", request);
        var jqlParts = new ArrayList<String>();

        if (!CollectionUtils.isEmpty(request.getProjects())) {
            var projects = request.getProjects();
            if (!projects.contains("*")) {
                var projectFilter = projects.stream()
                        .map(key -> "\"" + key + "\"")
                        .collect(Collectors.joining(","));
                jqlParts.add("project in (" + projectFilter + ")");
            }
        }

        // Date range filter
        if (StringUtils.isNotBlank(request.getStartDate())) {
            jqlParts.add("updated >= \"" + request.getStartDate() + "\"");
        }

        if (StringUtils.isNotBlank(request.getEndDate())) {
            jqlParts.add("updated <= \"" + request.getEndDate() + "\"");
        }


        var jql = jqlParts.isEmpty() ? "" : String.join(" AND ", jqlParts);
        jql += " ORDER BY updated DESC";

        log.debug("Finished building JQL: {}", jql.trim());
        return jql.trim();
    }
}