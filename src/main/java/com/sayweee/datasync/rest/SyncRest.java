package com.sayweee.datasync.rest;

import com.sayweee.core.framework.base.BaseResponse;
import com.sayweee.datasync.model.request.JiraSyncRequest;
import com.sayweee.datasync.model.response.SyncResponse;
import com.sayweee.datasync.service.AsyncSyncService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping()
@RequiredArgsConstructor
public class SyncRest {

    private final AsyncSyncService asyncSyncService;

    @PostMapping("/jira")
    public BaseResponse<SyncResponse> startJiraSync(@RequestBody JiraSyncRequest request) {
        request.applyDefaults();
        var taskId = asyncSyncService.submitJiraSync(request);
        var response = SyncResponse.success(taskId, "Jira同步任务已提交，正在后台执行");
        return BaseResponse.success(response);
    }
}