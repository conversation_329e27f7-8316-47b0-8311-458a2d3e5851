//package com.sayweee.datasync.service.jira;
//
//import com.sayweee.datasync.fetcher.jira.ChangelogFetcher;
//import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
//import com.sayweee.datasync.model.entity.ChangeLogEntity;
//import com.sayweee.datasync.writer.jira.ChangeLogWriter;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.stream.Stream;
//
//@Service
//@Slf4j
//@RequiredArgsConstructor
//public class ChangeLogService {
//    private static final String BINDING_NAME = "jira-changelog-events-out-0";
//    private final ChangelogFetcher changelogFetcher;
//    private final ChangeLogWriter changeLogWriter;
//    private final StreamBridge streamBridge;
//
//    public void processAndFetchChangelog(List<JiraIngestionPayload> events) {
//        Stream<ChangeLogEntity> changeLogStream = changelogFetcher.fetch("changelog-fetcher", events);
//        AtomicInteger total = new AtomicInteger(0);
//        changeLogStream.forEach(event -> {
//            streamBridge.send(BINDING_NAME, event);
//            total.incrementAndGet();
//        });
//
//        log.info("sent {} changelog events to {}", total.get(), BINDING_NAME);
//    }
//
//    public void processAndSaveChangelog(List<ChangeLogEntity> events) {
//        if (events == null || events.isEmpty()) {
//            log.info("No changelog events to process");
//            return;
//        }
//
//        try {
//            changeLogWriter.writeChangeLogs(events);
//        } catch (Exception e) {
//            log.error("Failed to process changelog events", e);
//            throw e;
//        }
//    }
//}
