package com.sayweee.datasync.model.entity;

import lombok.Getter;
import lombok.Setter;

/**
 * JIRA Issue 远程链接实体类
 * 对应数据库表: weee_jira_new.issue_remote_links
 */
@Getter
@Setter
public class RemoteLinkEntity {

    /**
     * 远程链接ID
     */
    private Long id;

    /**
     * Issue ID
     */
    private Integer issueId;

    /**
     * 远程链接标识
     */
    private String remoteLink;

    /**
     * 链接URL地址
     */
    private String url;

    /**
     * 链接标题
     */
    private String title;

    /**
     * 默认构造函数
     */
    public RemoteLinkEntity() {
    }

    /**
     * 全参数构造函数
     */
    public RemoteLinkEntity(Long id, Integer issueId, String remoteLink, String url, String title) {
        this.id = id;
        this.issueId = issueId;
        this.remoteLink = remoteLink;
        this.url = url;
        this.title = title;
    }

    @Override
    public String toString() {
        return "RemoteLinkEntity{" +
                "id=" + id +
                ", issueId=" + issueId +
                ", remoteLink='" + remoteLink + '\'' +
                ", url='" + url + '\'' +
                ", title='" + title + '\'' +
                '}';
    }
}
