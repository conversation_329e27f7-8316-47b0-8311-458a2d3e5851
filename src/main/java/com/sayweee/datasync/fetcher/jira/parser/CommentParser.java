package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.Issue;
import com.sayweee.datasync.model.entity.CommentEntity;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class CommentParser {

    private static final Pattern ISSUE_ID_PATTERN = Pattern.compile("/issue/(\\d+)/comment");

    public List<CommentEntity> parse(Issue issue) {
        List<CommentEntity> commentEntities = new ArrayList<>();

        if (issue.getComments() == null) {
            return commentEntities;
        }
        if ("ARCHI-2271".equals(issue.getKey())) {
            log.info("issue {} has comments", issue.getKey());
        }

        for (var comment : issue.getComments()) {
            try {
                CommentEntity commentEntity = parseComment(comment, issue.getId());
                commentEntities.add(commentEntity);
            } catch (Exception e) {
                log.warn("Failed to parse comment {} for issue {}: {}",
                        comment.getId(), issue.getKey(), e.getMessage(), e);
            }
        }
        log.info("parsed {} comments for issue {}", commentEntities.size(), issue.getKey());
        return commentEntities;
    }

    private CommentEntity parseComment(com.atlassian.jira.rest.client.api.domain.Comment comment, Long fallbackIssueId) {
        CommentEntity entity = new CommentEntity();

        // 设置评论ID
        entity.setId(comment.getId());

        // 从 comment.self 中提取 issueId，如果失败则使用 fallbackIssueId
        Long issueId = extractIssueIdFromSelf(comment.getSelf());
        entity.setIssueId(issueId != null ? issueId : fallbackIssueId);

        // 设置用户信息
        if (comment.getAuthor() != null) {
            entity.setUserid(comment.getAuthor().getAccountId());
            entity.setUsername(comment.getAuthor().getDisplayName());
        }

        // 设置时间信息
        entity.setCreated(convertDateTime(comment.getCreationDate()));
        entity.setUpdated(convertDateTime(comment.getUpdateDate()));

        // 解析评论内容
        entity.setComment(comment.getBody());

        return entity;
    }

    /**
     * 从 comment.self URL 中提取 issueId
     *
     * @param selfUrl comment.self URL
     * @return issueId 或 null
     */
    private Long extractIssueIdFromSelf(java.net.URI selfUrl) {
        if (selfUrl == null) {
            return null;
        }

        try {
            String url = selfUrl.toString();
            Matcher matcher = ISSUE_ID_PATTERN.matcher(url);
            if (matcher.find()) {
                return Long.parseLong(matcher.group(1));
            }
        } catch (Exception e) {
            log.warn("Failed to extract issueId from self URL: {}", selfUrl, e);
        }

        return null;
    }

    /**
     * 转换 Jira DateTime 到 OffsetDateTime
     */
    private OffsetDateTime convertDateTime(DateTime dateTime) {
        return dateTime == null ? null :
                OffsetDateTime.ofInstant(Instant.ofEpochMilli(dateTime.getMillis()), ZoneOffset.UTC);
    }
}