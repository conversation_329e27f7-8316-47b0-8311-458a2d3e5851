package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.common.constants.SyncConstants;
import com.sayweee.datasync.dao.RemoteLinkDao;
import com.sayweee.datasync.model.entity.RemoteLinkEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class RemoteLinkWriter {

    private final RemoteLinkDao remoteLinkDao;

    public void writeRemoteLinks(List<RemoteLinkEntity> remoteLinkEntities) {
        if (remoteLinkEntities == null || remoteLinkEntities.isEmpty()) {
            return;
        }

        try {
            int affectedRows = batchUpsertRemoteLinks(remoteLinkEntities);
            log.info("Successfully processed {} remote links", affectedRows);
        } catch (Exception e) {
            throw new RuntimeException("Failed to write remote links to database", e);
        }
    }

    private int batchUpsertRemoteLinks(List<RemoteLinkEntity> remoteLinkEntities) {
        String tempTableName = createTempTable();
        try {
            batchInsertToTempTable(tempTableName, remoteLinkEntities);
            return executeUpsertFromTempTable(tempTableName);
        } finally {
            dropTempTable(tempTableName);
        }
    }

    private String createTempTable() {
        String tempTableName = "weee_jira_new.temp_issue_remote_links_" + System.currentTimeMillis();
        try {
            remoteLinkDao.createTempTableLike(tempTableName, SyncConstants.Database.JIRA_REMOTE_LINKS_TABLE);
            return tempTableName;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create temp table", e);
        }
    }

    private void batchInsertToTempTable(String tempTableName, List<RemoteLinkEntity> remoteLinkEntities) {
        try {
            remoteLinkDao.batchInsert(tempTableName, remoteLinkEntities);
        } catch (Exception e) {
            throw new RuntimeException("Failed to insert data into temp table", e);
        }
    }

    private int executeUpsertFromTempTable(String tempTableName) {
        try {
            String targetTable = SyncConstants.Database.JIRA_REMOTE_LINKS_TABLE;
            int deletedRows = remoteLinkDao.deleteFromTargetUsingTemp(targetTable, tempTableName);
            int insertedRows = remoteLinkDao.insertFromTempToTarget(targetTable, tempTableName);
            return deletedRows + insertedRows;
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute UPSERT operation", e);
        }
    }

    private void dropTempTable(String tempTableName) {
        try {
            remoteLinkDao.dropTable(tempTableName);
        } catch (Exception e) {
            log.warn("Failed to drop temp table: {}", tempTableName, e);
        }
    }
}
