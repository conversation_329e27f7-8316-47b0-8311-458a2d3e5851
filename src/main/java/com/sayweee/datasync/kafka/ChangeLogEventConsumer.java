//package com.sayweee.datasync.kafka;
//
//import com.sayweee.datasync.model.entity.ChangeLogEntity;
//import com.sayweee.datasync.service.jira.ChangeLogService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.List;
//import java.util.function.Consumer;
//
//@Configuration
//@Slf4j
//@RequiredArgsConstructor
//public class ChangeLogEventConsumer {
//    private final ChangeLogService changeLogService;
//
//    @Bean
//    public Consumer<Message<List<ChangeLogEntity>>> consumeAndSaveChangeLog() {
//        return message -> {
//            var events = message.getPayload();
//            if (events.isEmpty()) {
//                return;
//            }
//            Acknowledgment acknowledgment = message.getHeaders().get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment.class);
//
//            log.info("Received a batch of {} changelog events to process.", events.size());
//            changeLogService.processAndSaveChangelog(events);
//            log.info("Received a batch of {} events to process.", events.size());
//            if (acknowledgment != null) {
//                acknowledgment.acknowledge();
//            }
//        };
//    }
//}
