package com.sayweee.datasync.fetcher.jira.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sayweee.datasync.model.entity.RemoteLinkEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class RemoteLinkParserTest {

    @InjectMocks
    private RemoteLinkParser remoteLinkParser;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    void testParseValidRemoteLinks() throws Exception {
        // Given
        String jsonString = """
            [
                {
                    "id": 100,
                    "self": "http://localhost:8090/jira/rest/api/latest/issue/TEST-1/remotelink/100",
                    "object": {
                        "url": "http://www.example.com/support?id=1",
                        "title": "Support Issue #1"
                    }
                },
                {
                    "id": 101,
                    "self": "http://localhost:8090/jira/rest/api/latest/issue/TEST-1/remotelink/101",
                    "object": {
                        "url": "http://www.example.com/support?id=2",
                        "title": "Support Issue #2"
                    }
                }
            ]
            """;
        
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        Integer issueId = 12345;

        // When
        List<RemoteLinkEntity> result = remoteLinkParser.parse(jsonNode, issueId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        RemoteLinkEntity firstLink = result.get(0);
        assertEquals(100L, firstLink.getId());
        assertEquals(issueId, firstLink.getIssueId());
        assertEquals("http://localhost:8090/jira/rest/api/latest/issue/TEST-1/remotelink/100", firstLink.getRemoteLink());
        assertEquals("http://www.example.com/support?id=1", firstLink.getUrl());
        assertEquals("Support Issue #1", firstLink.getTitle());

        RemoteLinkEntity secondLink = result.get(1);
        assertEquals(101L, secondLink.getId());
        assertEquals(issueId, secondLink.getIssueId());
        assertEquals("http://localhost:8090/jira/rest/api/latest/issue/TEST-1/remotelink/101", secondLink.getRemoteLink());
        assertEquals("http://www.example.com/support?id=2", secondLink.getUrl());
        assertEquals("Support Issue #2", secondLink.getTitle());
    }

    @Test
    void testParseEmptyArray() throws Exception {
        // Given
        String jsonString = "[]";
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        Integer issueId = 12345;

        // When
        List<RemoteLinkEntity> result = remoteLinkParser.parse(jsonNode, issueId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseNullNode() {
        // Given
        JsonNode jsonNode = null;
        Integer issueId = 12345;

        // When
        List<RemoteLinkEntity> result = remoteLinkParser.parse(jsonNode, issueId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseNonArrayNode() throws Exception {
        // Given
        String jsonString = """
            {
                "id": 100,
                "self": "http://localhost:8090/jira/rest/api/latest/issue/TEST-1/remotelink/100"
            }
            """;
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        Integer issueId = 12345;

        // When
        List<RemoteLinkEntity> result = remoteLinkParser.parse(jsonNode, issueId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseRemoteLinkWithMissingFields() throws Exception {
        // Given
        String jsonString = """
            [
                {
                    "id": 100,
                    "object": {
                        "url": "http://www.example.com/support?id=1"
                    }
                },
                {
                    "self": "http://localhost:8090/jira/rest/api/latest/issue/TEST-1/remotelink/101",
                    "object": {
                        "title": "Support Issue #2"
                    }
                }
            ]
            """;
        
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        Integer issueId = 12345;

        // When
        List<RemoteLinkEntity> result = remoteLinkParser.parse(jsonNode, issueId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        RemoteLinkEntity firstLink = result.get(0);
        assertEquals(100L, firstLink.getId());
        assertEquals(issueId, firstLink.getIssueId());
        assertNull(firstLink.getRemoteLink());
        assertEquals("http://www.example.com/support?id=1", firstLink.getUrl());
        assertNull(firstLink.getTitle());

        RemoteLinkEntity secondLink = result.get(1);
        assertNull(secondLink.getId());
        assertEquals(issueId, secondLink.getIssueId());
        assertEquals("http://localhost:8090/jira/rest/api/latest/issue/TEST-1/remotelink/101", secondLink.getRemoteLink());
        assertNull(secondLink.getUrl());
        assertEquals("Support Issue #2", secondLink.getTitle());
    }

    @Test
    void testParseRemoteLinkWithoutObjectNode() throws Exception {
        // Given
        String jsonString = """
            [
                {
                    "id": 100,
                    "self": "http://localhost:8090/jira/rest/api/latest/issue/TEST-1/remotelink/100"
                }
            ]
            """;
        
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        Integer issueId = 12345;

        // When
        List<RemoteLinkEntity> result = remoteLinkParser.parse(jsonNode, issueId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        RemoteLinkEntity link = result.get(0);
        assertEquals(100L, link.getId());
        assertEquals(issueId, link.getIssueId());
        assertEquals("http://localhost:8090/jira/rest/api/latest/issue/TEST-1/remotelink/100", link.getRemoteLink());
        assertNull(link.getUrl());
        assertNull(link.getTitle());
    }
}
