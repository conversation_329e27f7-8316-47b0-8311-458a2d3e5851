package com.sayweee.datasync.fetcher.jira.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.sayweee.core.framework.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import java.util.Set;

/**
 * JIRA HTTP 客户端，用于发送HTTP请求到JIRA API
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JiraHttpClient {

    private final HttpClient httpClient;

    @Value("${jira.server.uri}")
    private URI JIRA_SERVER_URI;

    @Value("${jira.email}")
    private String JIRA_EMAIL;

    @Value("${jira.api.token}")
    private String JIRA_API_TOKEN;

    /**
     * 发送POST请求到JIRA API并返回JsonNode响应
     *
     * @param endpoint    API端点路径（相对于JIRA服务器URI）
     * @param jsonPayload 请求体JSON数据
     * @return 解析后的JsonNode响应
     * @throws IOException          当HTTP请求失败或响应状态码不正确时抛出
     * @throws InterruptedException 当HTTP请求被中断时抛出
     */
    public JsonNode sendPostRequest(String endpoint, String jsonPayload)
            throws IOException, InterruptedException {

        String url = JIRA_SERVER_URI + endpoint;
        String authHeader = createAuthHeader();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                .header("Authorization", authHeader)
                .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                .build();

        log.debug(" Sending POST request to: {}", url);

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() < 200 || response.statusCode() >= 300) {
            log.error("Task [{}] - HTTP request failed. Status: {}, Body: {}", response.statusCode(), response.body());
            throw new IOException(" Unexpected response code: " +
                    response.statusCode() + "\nBody: " + response.body());
        }

        log.debug("HTTP request successful. Status: {}", response.statusCode());

        return JacksonUtils.instance().readTree(response.body());
    }

    /**
     * 发送GET请求到JIRA API并返回JsonNode响应
     *
     * @param taskId   任务ID，用于日志记录
     * @param endpoint API端点路径（相对于JIRA服务器URI）
     * @return 解析后的JsonNode响应
     * @throws IOException          当HTTP请求失败或响应状态码不正确时抛出
     * @throws InterruptedException 当HTTP请求被中断时抛出
     */
    public JsonNode sendGetRequest(String taskId, String endpoint)
            throws IOException, InterruptedException {

        String url = JIRA_SERVER_URI + endpoint;
        String authHeader = createAuthHeader();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Accept", "application/json")
                .header("Authorization", authHeader)
                .GET()
                .build();

        log.debug("Task [{}] - Sending GET request to: {}", taskId, url);

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() < 200 || response.statusCode() >= 300) {
            log.error("Task [{}] - HTTP request failed. Status: {}, Body: {}",
                    taskId, response.statusCode(), response.body());
            throw new IOException("Task [" + taskId + "] - Unexpected response code: " +
                    response.statusCode() + "\nBody: " + response.body());
        }

        log.debug("Task [{}] - HTTP request successful. Status: {}", taskId, response.statusCode());

        return JacksonUtils.instance().readTree(response.body());
    }

    public JsonNode searchIssues(String jql, Set<String> fields, Integer maxResults,
                                 String nextPageToken, List<Integer> reconcileIssues)
            throws IOException, InterruptedException {

        String endpoint = "/rest/api/3/search/jql";
        String jsonPayload = createSearchPayload(jql, fields, maxResults, nextPageToken, reconcileIssues);

        return sendPostRequest(endpoint, jsonPayload);
    }

    public JsonNode searchIssues(String jql, Set<String> fields, Integer maxResults)
            throws IOException, InterruptedException {
        return searchIssues(jql, fields, maxResults, null, null);
    }

    public JsonNode searchIssues(String taskId, String jql) throws IOException, InterruptedException {
        return searchIssues(jql, null, 50, null, null);
    }

    public URI getJiraServerUri() {
        return JIRA_SERVER_URI;
    }

    public HttpRequest.Builder createAuthenticatedRequestBuilder(URI uri) {
        String authHeader = createAuthHeader();

        return HttpRequest.newBuilder()
                .uri(uri)
                .header("Accept", "application/json")
                .header("Authorization", authHeader);
    }

    private String createSearchPayload(String jql, Set<String> fields, Integer maxResults,
                                       String nextPageToken, List<Integer> reconcileIssues) {
        try {
            var objectMapper = JacksonUtils.instance();
            var payload = objectMapper.createObjectNode();

            // 设置基本参数
            if (jql != null && !jql.trim().isEmpty()) {
                payload.put("jql", jql);
            }

            if (maxResults != null) {
                payload.put("maxResults", maxResults);
            } else {
                payload.put("maxResults", 50); // 默认值
            }

            if (nextPageToken != null && !nextPageToken.trim().isEmpty()) {
                payload.put("nextPageToken", nextPageToken);
            }

            // 设置字段
            if (fields != null && !fields.isEmpty()) {
                var fieldsArray = payload.putArray("fields");
                fields.forEach(fieldsArray::add);
                payload.put("fieldsByKeys", true);
            }

            // 设置协调Issues
            if (reconcileIssues != null && !reconcileIssues.isEmpty()) {
                var reconcileArray = payload.putArray("reconcileIssues");
                reconcileIssues.forEach(reconcileArray::add);
            }

            // 设置扩展信息
            payload.put("expand", "names,schema,operations,editmeta,changelog,renderedFields");

            // 设置属性
            var propertiesArray = payload.putArray("properties");
            propertiesArray.add("*all");

            return objectMapper.writeValueAsString(payload);

        } catch (Exception e) {
            log.error("Failed to create search payload", e);
            throw new RuntimeException("Failed to create search payload", e);
        }
    }

    private String createAuthHeader() {
        String credentials = JIRA_EMAIL + ":" + JIRA_API_TOKEN;
        return "Basic " + Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
    }
}