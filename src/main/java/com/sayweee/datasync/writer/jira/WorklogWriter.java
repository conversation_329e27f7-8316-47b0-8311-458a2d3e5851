package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.common.constants.SyncConstants;
import com.sayweee.datasync.dao.WorklogDao;
import com.sayweee.datasync.model.entity.WorklogEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class WorklogWriter {

    private final WorklogDao worklogDao;

    public void writeWorklogs(List<WorklogEntity> worklogEntities) {
        if (worklogEntities == null || worklogEntities.isEmpty()) {
            return;
        }

        try {
            int affectedRows = batchUpsertWorklogs(worklogEntities);
            log.info("Successfully processed {} worklogs", affectedRows);
        } catch (Exception e) {
            throw new RuntimeException("Failed to write worklogs to database", e);
        }
    }

    private int batchUpsertWorklogs(List<WorklogEntity> worklogEntities) {
        String tempTableName = createTempTable();
        try {
            batchInsertToTempTable(tempTableName, worklogEntities);
            return executeUpsertFromTempTable(tempTableName);
        } finally {
            dropTempTable(tempTableName);
        }
    }

    private String createTempTable() {
        String tempTableName = "weee_jira_new.temp_issue_timelog_" + System.currentTimeMillis();
        try {
            worklogDao.createTempTableLike(tempTableName, SyncConstants.Database.JIRA_WORKLOGS_TABLE);
            return tempTableName;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create temp table", e);
        }
    }

    private void batchInsertToTempTable(String tempTableName, List<WorklogEntity> worklogEntities) {
        try {
            worklogDao.batchInsert(tempTableName, worklogEntities);
        } catch (Exception e) {
            throw new RuntimeException("Failed to insert data into temp table", e);
        }
    }

    private int executeUpsertFromTempTable(String tempTableName) {
        try {
            String targetTable = SyncConstants.Database.JIRA_WORKLOGS_TABLE;
            int deletedRows = worklogDao.deleteFromTargetUsingTemp(targetTable, tempTableName);
            int insertedRows = worklogDao.insertFromTempToTarget(targetTable, tempTableName);
            return deletedRows + insertedRows;
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute UPSERT operation", e);
        }
    }

    private void dropTempTable(String tempTableName) {
        try {
            worklogDao.dropTable(tempTableName);
        } catch (Exception e) {
            log.warn("Failed to drop temp table: {}", tempTableName, e);
        }
    }
}