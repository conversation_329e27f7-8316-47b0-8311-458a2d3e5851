package com.sayweee.datasync.fetcher.jira.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.sayweee.datasync.model.entity.RemoteLinkEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class RemoteLinkParser {

    /**
     * 解析 JIRA 远程链接 JSON 数据
     *
     * @param remoteLinkNode JIRA 远程链接 JSON 节点
     * @param issueId Issue ID
     * @return 远程链接实体列表
     */
    public List<RemoteLinkEntity> parse(JsonNode remoteLinkNode, Integer issueId) {
        List<RemoteLinkEntity> remoteLinkEntities = new ArrayList<>();

        if (remoteLinkNode == null || !remoteLinkNode.isArray()) {
            return remoteLinkEntities;
        }

        for (JsonNode linkNode : remoteLinkNode) {
            try {
                RemoteLinkEntity entity = parseRemoteLink(linkNode, issueId);
                if (entity != null) {
                    remoteLinkEntities.add(entity);
                }
            } catch (Exception e) {
                log.warn("Failed to parse remote link for issue {}: {}", issueId, e.getMessage(), e);
            }
        }

        return remoteLinkEntities;
    }

    /**
     * 解析单个远程链接
     *
     * @param linkNode 远程链接 JSON 节点
     * @param issueId Issue ID
     * @return RemoteLinkEntity 或 null
     */
    private RemoteLinkEntity parseRemoteLink(JsonNode linkNode, Integer issueId) {
        if (linkNode == null) {
            return null;
        }

        RemoteLinkEntity entity = new RemoteLinkEntity();

        // 设置 Issue ID
        entity.setIssueId(issueId);

        // 设置远程链接 ID
        if (linkNode.has("id")) {
            entity.setId(linkNode.get("id").asLong());
        }

        // 设置远程链接标识 (self URL)
        if (linkNode.has("self")) {
            entity.setRemoteLink(linkNode.get("self").asText());
        }

        // 解析 object 节点获取 URL 和 title
        if (linkNode.has("object")) {
            JsonNode objectNode = linkNode.get("object");

            if (objectNode.has("url")) {
                entity.setUrl(objectNode.get("url").asText());
            }

            if (objectNode.has("title")) {
                entity.setTitle(objectNode.get("title").asText());
            }
        }

        return entity;
    }
}
