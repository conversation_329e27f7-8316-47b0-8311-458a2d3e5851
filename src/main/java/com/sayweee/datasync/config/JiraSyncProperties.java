package com.sayweee.datasync.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "jira.sync")
public class JiraSyncProperties {
    private int maxResultsPerPage = 50;
    private int maxJqlLength = 8000;
    private Duration searchTimeout = Duration.ofSeconds(30);
    private int maxRetryAttempts = 3;
    private Duration retryDelay = Duration.ofSeconds(2);
}