package com.sayweee.datasync.fetcher.jira;

import com.fasterxml.jackson.databind.JsonNode;
import com.sayweee.datasync.fetcher.jira.client.JiraHttpClient;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.fetcher.jira.parser.WorklogParser;
import com.sayweee.datasync.model.entity.WorklogEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
@Component
@RequiredArgsConstructor
public class WorklogFetcher {
    private final JiraHttpClient httpClient;
    private final WorklogParser worklogParser;

    private record WorklogPageState(List<WorklogEntity> currentPageWorklogs, boolean hasMore, int currentIndex) {
    }

    private record WorklogPageResult(List<WorklogEntity> worklogs, boolean hasMore) {
    }

    /**
     * 获取指定 Issues 的 Worklog 数据流
     *
     * @param taskId 任务ID
     * @param events JIRA 事件列表
     * @return Worklog 实体流
     */
    public Stream<WorklogEntity> fetch(String taskId, List<JiraIngestionPayload> events) {
        var issueIdList = events
                .stream()
                .map(JiraIngestionPayload::issue)
                .map(issue -> issue.id().toString())
                .toList();

        log.info("Task [{}] - Starting to fetch JIRA worklogs for {} issues.", taskId, issueIdList.size());

        try {
            // 1. 获取初始页面数据以启动流
            WorklogPageResult initialResult = fetchWorklogsForIssues(taskId, issueIdList, 0);
            log.info("Task [{}] - Initial worklogs fetch complete.", taskId);

            // 2. 为流迭代器创建初始状态
            WorklogPageState initialState = new WorklogPageState(
                    initialResult.worklogs(),
                    initialResult.hasMore(),
                    0
            );

            return Stream.iterate(
                            initialState,
                            state -> !state.currentPageWorklogs().isEmpty(),
                            currentState -> {
                                if (!currentState.hasMore()) {
                                    log.info("Task [{}] - Reached the last issue, no more worklogs to fetch.", taskId);
                                    return new WorklogPageState(Collections.emptyList(), false, currentState.currentIndex());
                                }

                                int nextIndex = currentState.currentIndex() + 1;
                                log.debug("Task [{}] - Fetching worklogs for next issue. Index: {}", taskId, nextIndex);

                                try {
                                    WorklogPageResult nextResult = fetchWorklogsForIssues(taskId, issueIdList, nextIndex);
                                    return new WorklogPageState(
                                            nextResult.worklogs(),
                                            nextResult.hasMore() && nextIndex < issueIdList.size() - 1,
                                            nextIndex
                                    );
                                } catch (IOException | InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                    throw new RuntimeException("Task [" + taskId + "] - Failed to fetch worklogs for subsequent issue", e);
                                }
                            }
                    )
                    .flatMap(state -> state.currentPageWorklogs().stream());

        } catch (Exception e) {
            log.error("Task [{}] - Failed to fetch JIRA worklogs.", taskId, e);
            throw new RuntimeException("Task [" + taskId + "] - Failed to fetch JIRA worklogs", e);
        }
    }

    /**
     * 获取指定 Issues 的 Worklog 数据
     *
     * @param taskId 任务ID
     * @param issueIdList Issue ID 列表
     * @param currentIndex 当前处理的 Issue 索引
     * @return Worklog 页面结果
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    private WorklogPageResult fetchWorklogsForIssues(String taskId, List<String> issueIdList, int currentIndex)
            throws IOException, InterruptedException {

        if (currentIndex >= issueIdList.size()) {
            return new WorklogPageResult(Collections.emptyList(), false);
        }

        String issueId = issueIdList.get(currentIndex);
        String endpoint = "/rest/api/3/issue/" + issueId + "/worklog";

        log.debug("Task [{}] - Fetching worklogs for issue: {}", taskId, issueId);

        JsonNode responseNode = httpClient.sendGetRequest(taskId, endpoint);

        List<WorklogEntity> worklogs = new ArrayList<>();
        if (responseNode != null) {
            try {
                worklogs.addAll(worklogParser.parse(responseNode, Integer.valueOf(issueId)));
            } catch (Exception e) {
                log.warn("Task [{}] - Failed to parse worklogs for issue {}: {}", taskId, issueId, e.getMessage(), e);
            }
        }

        boolean hasMore = currentIndex < issueIdList.size() - 1;
        log.debug("Task [{}] - Fetched {} worklogs for issue: {}, hasMore: {}", taskId, worklogs.size(), issueId, hasMore);

        return new WorklogPageResult(worklogs, hasMore);
    }
}