package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.dao.WorklogDao;
import com.sayweee.datasync.model.entity.WorklogEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WorklogWriterTest {

    @Mock
    private WorklogDao worklogDao;

    @InjectMocks
    private WorklogWriter worklogWriter;

    private List<WorklogEntity> testWorklogs;

    @BeforeEach
    void setUp() {
        OffsetDateTime now = OffsetDateTime.now();
        testWorklogs = Arrays.asList(
                new WorklogEntity(1, now, 12345, now, now, "1h", 3600, "Test work 1", "user1", "User One"),
                new WorklogEntity(2, now, 12346, now, now, "2h", 7200, "Test work 2", "user2", "User Two")
        );
    }

    @Test
    void testWriteWorklogs_WithNullList_ShouldReturnEarly() {
        // When
        assertDoesNotThrow(() -> worklogWriter.writeWorklogs(null));

        // Then
        verifyNoInteractions(worklogDao);
    }

    @Test
    void testWriteWorklogs_WithEmptyList_ShouldReturnEarly() {
        // When
        assertDoesNotThrow(() -> worklogWriter.writeWorklogs(Collections.emptyList()));

        // Then
        verifyNoInteractions(worklogDao);
    }

    @Test
    void testWriteWorklogs_WithValidData_ShouldProcessSuccessfully() {
        // Given
        when(worklogDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(2);
        when(worklogDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(3);

        // When
        assertDoesNotThrow(() -> worklogWriter.writeWorklogs(testWorklogs));

        // Then
        verify(worklogDao).createTempTableLike(anyString(), eq("weee_jira_new.issue_timelog"));
        verify(worklogDao).batchInsert(anyString(), eq(testWorklogs));
        verify(worklogDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(worklogDao).insertFromTempToTarget(anyString(), anyString());
        verify(worklogDao).dropTable(anyString());
    }

    @Test
    void testWriteWorklogs_WhenCreateTempTableFails_ShouldThrowException() {
        // Given
        doThrow(new RuntimeException("Database error")).when(worklogDao)
                .createTempTableLike(anyString(), anyString());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> worklogWriter.writeWorklogs(testWorklogs));
        
        assertTrue(exception.getMessage().contains("Failed to write worklogs to database"));
    }

    @Test
    void testWriteWorklogs_WhenBatchInsertFails_ShouldThrowExceptionAndCleanup() {
        // Given
        doThrow(new RuntimeException("Insert error")).when(worklogDao)
                .batchInsert(anyString(), anyList());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> worklogWriter.writeWorklogs(testWorklogs));
        
        assertTrue(exception.getMessage().contains("Failed to write worklogs to database"));
        
        // Verify cleanup is attempted
        verify(worklogDao).dropTable(anyString());
    }

    @Test
    void testWriteWorklogs_WhenUpsertFails_ShouldThrowExceptionAndCleanup() {
        // Given
        doThrow(new RuntimeException("Upsert error")).when(worklogDao)
                .deleteFromTargetUsingTemp(anyString(), anyString());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> worklogWriter.writeWorklogs(testWorklogs));
        
        assertTrue(exception.getMessage().contains("Failed to write worklogs to database"));
        
        // Verify cleanup is attempted
        verify(worklogDao).dropTable(anyString());
    }
}