package com.sayweee.datasync.fetcher.jira;

import com.atlassian.jira.rest.client.api.JiraRestClient;
import com.atlassian.jira.rest.client.api.domain.SearchResult;
import com.sayweee.datasync.config.JiraSyncProperties;
import com.sayweee.datasync.fetcher.jira.builder.JqlQueryBuilder;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.fetcher.jira.parser.JiraPayloadParser;
import com.sayweee.datasync.model.request.JiraSyncRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class IssueFetcher {
    private final JiraSyncProperties syncProperties;
    private final JqlQueryBuilder jqlQueryBuilder;
    private final JiraPayloadParser parser;
    private final JiraRestClient jiraRestClient;

    private record JiraPagedState(List<JiraIngestionPayload> currentPageContent, String nextPageToken) {
    }

    private record QueryParams(String jql, Set<String> fields) {
    }

    public Stream<List<JiraIngestionPayload>> getIssuePages(String taskId, JiraSyncRequest request) {
        log.info("Task [{}] - Starting to fetch JIRA issues by pages. Request: {}", taskId, request);

        String jql = jqlQueryBuilder.buildJql(request);
        Set<String> fields = request.getFields();
        QueryParams queryParams = new QueryParams(jql, fields);

        return Stream.iterate(
                        fetchInitialPage(taskId, queryParams),
                        Objects::nonNull,
                        currentState -> fetchNextPage(taskId, queryParams, currentState)
                )
                .takeWhile(state -> !state.currentPageContent().isEmpty())
                .map(JiraPagedState::currentPageContent)
                .onClose(() -> log.debug("Task [{}] - Issue stream closed", taskId));
    }

    private JiraPagedState fetchNextPage(String taskId, QueryParams queryParams, JiraPagedState currentState) {
        if (StringUtils.isEmpty(currentState.nextPageToken())) {
            log.info("Task [{}] - Reached the last page, no more nextPageToken.", taskId);
            return null;
        }

        try {
            log.debug("Task [{}] - Fetching next page of data. Token: {}", taskId, currentState.nextPageToken());

            SearchResult nextResult = searchIssues(taskId, queryParams.jql(), currentState.nextPageToken(), queryParams.fields());
            return new JiraPagedState(
                    parser.parse(nextResult.getIssues()),
                    nextResult.getNextPageToken()
            );
        } catch (Exception e) {
            log.error("Task [{}] - Failed to fetch next JIRA page", taskId, e);
            throw e;
        }
    }

    private JiraPagedState fetchInitialPage(String taskId, QueryParams queryParams) {
        try {
            log.info("Task [{}] - Constructed JQL query: {}", taskId, queryParams.fields());

            SearchResult initialResult = searchIssues(taskId, queryParams.jql(), null, queryParams.fields());
            log.info("Task [{}] - Initial search complete.", taskId);

            return new JiraPagedState(
                    parser.parse(initialResult.getIssues()),
                    initialResult.getNextPageToken()
            );
        } catch (Exception e) {
            log.error("Task [{}] - Failed to fetch initial JIRA page for request: {}", taskId, queryParams, e);
            throw e;
        }
    }

    private SearchResult searchIssues(String taskId, String jql, String nextPageToken, Set<String> fields) {
        try {
            log.debug("Task [{}] - Executing JQL search. PageToken: '{}', Fields: {}", taskId, nextPageToken, fields);
            var searchClient = jiraRestClient.getSearchClient();

            var searchResultPromise = searchClient.enhancedSearchJql(jql, syncProperties.getMaxResultsPerPage(), nextPageToken, fields, null);
            SearchResult result = searchResultPromise.get();
            log.debug("Task [{}] - JQL search successful.", taskId);
            return result;
        } catch (Exception e) {
            log.error("Task [{}] - JQL search execution failed. JQL: '{}', PageToken: '{}'", taskId, jql, nextPageToken, e);
            throw new RuntimeException("JQL search execution failed: " + jql, e);
        }
    }
}