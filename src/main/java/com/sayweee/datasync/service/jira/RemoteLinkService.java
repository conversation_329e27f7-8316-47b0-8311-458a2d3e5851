//package com.sayweee.datasync.service.jira;
//
//import com.sayweee.datasync.fetcher.jira.RemoteLinkFetcher;
//import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
//import com.sayweee.datasync.model.entity.RemoteLinkEntity;
//import com.sayweee.datasync.writer.jira.RemoteLinkWriter;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.cloud.stream.function.StreamBridge;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.stream.Stream;
//
//
//@Service
//@Slf4j
//@RequiredArgsConstructor
//public class RemoteLinkService {
//    private static final String BINDING_NAME = "jira-remotelink-events-out-0";
//    private final StreamBridge streamBridge;
//    private final RemoteLinkWriter remoteLinkWriter;
//    private final RemoteLinkFetcher remoteLinkFetcher;
//
//    /**
//     * 处理并获取远程链接数据
//     *
//     * @param events JIRA 事件列表
//     */
//    public void processAndFetchRemoteLink(List<JiraIngestionPayload> events) {
//        if (events == null || events.isEmpty()) {
//            log.info("No events to process for remote links");
//            return;
//        }
//
//        String taskId = "remotelink-fetch-" + System.currentTimeMillis();
//        AtomicInteger total = new AtomicInteger(0);
//
//        log.info("Task [{}] - Starting to process {} events for remote links", taskId, events.size());
//
//        try {
//            Stream<RemoteLinkEntity> remoteLinkStream = remoteLinkFetcher.fetch(taskId, events);
//
//            remoteLinkStream.forEach(remoteLinkEntity -> {
//                streamBridge.send(BINDING_NAME, remoteLinkEntity);
//                total.incrementAndGet();
//            });
//
//            log.info("Task [{}] - Successfully processed {} remote links", taskId, total.get());
//        } catch (Exception e) {
//            log.error("Task [{}] - Failed to process remote links", taskId, e);
//            throw new RuntimeException("Failed to process remote links for task: " + taskId, e);
//        }
//    }
//
//    public void processAndSaveRemoteLink(List<RemoteLinkEntity> events) {
//        if (events == null || events.isEmpty()) {
//            log.info("No remote links to process");
//            return;
//        }
//
//        try {
//            remoteLinkWriter.writeRemoteLinks(events);
//            log.info("Successfully processed {} remote links", events.size());
//        } catch (Exception e) {
//            log.error("Failed to process remote links", e);
//            throw e;
//        }
//    }
//}
