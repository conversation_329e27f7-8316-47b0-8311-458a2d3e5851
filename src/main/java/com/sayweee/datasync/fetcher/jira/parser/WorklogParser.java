package com.sayweee.datasync.fetcher.jira.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.sayweee.datasync.model.entity.WorklogEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class WorklogParser {

    private static final DateTimeFormatter JIRA_DATETIME_FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

    /**
     * 解析 JIRA Worklog JSON 数据
     *
     * @param worklogNode JIRA Worklog JSON 节点
     * @param issueId Issue ID
     * @return Worklog 实体列表
     */
    public List<WorklogEntity> parse(JsonNode worklogNode, Integer issueId) {
        List<WorklogEntity> worklogEntities = new ArrayList<>();

        if (worklogNode == null) {
            return worklogEntities;
        }

        // 处理分页结构 - worklogs 数组在 worklogs 字段中
        JsonNode worklogsArray = worklogNode.has("worklogs") ? worklogNode.get("worklogs") : worklogNode;
        
        if (!worklogsArray.isArray()) {
            return worklogEntities;
        }

        for (JsonNode worklogItemNode : worklogsArray) {
            try {
                WorklogEntity entity = parseWorklog(worklogItemNode, issueId);
                if (entity != null) {
                    worklogEntities.add(entity);
                }
            } catch (Exception e) {
                log.warn("Failed to parse worklog for issue {}: {}", issueId, e.getMessage(), e);
            }
        }

        return worklogEntities;
    }

    /**
     * 解析单个 Worklog
     *
     * @param worklogNode Worklog JSON 节点
     * @param issueId Issue ID
     * @return WorklogEntity 或 null
     */
    private WorklogEntity parseWorklog(JsonNode worklogNode, Integer issueId) {
        if (worklogNode == null) {
            return null;
        }

        WorklogEntity entity = new WorklogEntity();

        // 设置 Issue ID
        entity.setIssueId(issueId);

        // 设置 Worklog ID
        if (worklogNode.has("id")) {
            try {
                entity.setId(Integer.valueOf(worklogNode.get("id").asText()));
            } catch (NumberFormatException e) {
                log.warn("Invalid worklog ID format: {}", worklogNode.get("id").asText());
            }
        }

        // 设置创建时间
        if (worklogNode.has("created")) {
            entity.setCreated(parseDateTime(worklogNode.get("created").asText()));
        }

        // 设置开始时间
        if (worklogNode.has("started")) {
            entity.setStarted(parseDateTime(worklogNode.get("started").asText()));
        }

        // 设置更新时间
        if (worklogNode.has("updated")) {
            entity.setUpdated(parseDateTime(worklogNode.get("updated").asText()));
        }

        // 设置时间花费描述
        if (worklogNode.has("timeSpent")) {
            entity.setTimeSpent(worklogNode.get("timeSpent").asText());
        }

        // 设置时间花费秒数
        if (worklogNode.has("timeSpentSeconds")) {
            entity.setTimeSpentSeconds(worklogNode.get("timeSpentSeconds").asInt());
        }

        // 设置评论
        if (worklogNode.has("comment")) {
            JsonNode commentNode = worklogNode.get("comment");
            if (commentNode.isTextual()) {
                entity.setComment(commentNode.asText());
            } else if (commentNode.has("content")) {
                // 处理 ADF (Atlassian Document Format) 格式的评论
                entity.setComment(extractTextFromADF(commentNode));
            }
        }

        // 设置用户信息
        if (worklogNode.has("author")) {
            JsonNode authorNode = worklogNode.get("author");
            if (authorNode.has("accountId")) {
                entity.setUserId(authorNode.get("accountId").asText());
            }
            if (authorNode.has("displayName")) {
                entity.setUsername(authorNode.get("displayName").asText());
            }
        }

        return entity;
    }

    /**
     * 解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @return OffsetDateTime 或 null
     */
    private OffsetDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            return OffsetDateTime.parse(dateTimeStr, JIRA_DATETIME_FORMATTER);
        } catch (DateTimeParseException e) {
            log.warn("Failed to parse datetime: {}", dateTimeStr, e);
            return null;
        }
    }

    /**
     * 从 ADF 格式中提取纯文本
     *
     * @param adfNode ADF JSON 节点
     * @return 提取的文本
     */
    private String extractTextFromADF(JsonNode adfNode) {
        StringBuilder text = new StringBuilder();
        extractTextRecursive(adfNode, text);
        return text.toString().trim();
    }

    /**
     * 递归提取 ADF 中的文本内容
     *
     * @param node ADF 节点
     * @param text 文本构建器
     */
    private void extractTextRecursive(JsonNode node, StringBuilder text) {
        if (node == null) {
            return;
        }

        if (node.has("text")) {
            text.append(node.get("text").asText());
        }

        if (node.has("content") && node.get("content").isArray()) {
            for (JsonNode contentNode : node.get("content")) {
                extractTextRecursive(contentNode, text);
            }
        }
    }
}