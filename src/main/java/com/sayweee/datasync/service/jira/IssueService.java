package com.sayweee.datasync.service.jira;


import com.sayweee.datasync.fetcher.jira.IssueFetcher;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.model.entity.CommentEntity;
import com.sayweee.datasync.model.entity.IssueEntity;
import com.sayweee.datasync.model.entity.LinkedIssueEntity;
import com.sayweee.datasync.model.request.JiraSyncRequest;
import com.sayweee.datasync.writer.jira.CommentWriter;
import com.sayweee.datasync.writer.jira.IssueWriter;
import com.sayweee.datasync.writer.jira.LinkedIssueWriter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;


@Slf4j
@Service
@RequiredArgsConstructor
public class IssueService {
    private final IssueFetcher issueFetcher;
    private final IssueWriter issueWriter;
    private final CommentWriter commentWriter;
    private final LinkedIssueWriter linkedIssueWriter;


    public void syncIssues(String taskId, JiraSyncRequest request) {
        AtomicInteger total = new AtomicInteger(0);

        log.info("开始同步Jira任务: {}", taskId);

        Stream<List<JiraIngestionPayload>> issuesStream = issueFetcher.getIssuePages(taskId, request);

        issuesStream.forEach(payload -> {
            processAndSaveIssue(payload);
            total.addAndGet(payload.size());
            log.info("task [{}] synced {} issues, total {}", taskId, payload.size(), total.get());
        });

        log.info("task [{}] synced successfully，total {}", taskId, total.get());
    }

    private void processAndSaveIssue(List<JiraIngestionPayload> events) {
        List<IssueEntity> issueEntities = events.stream().map(JiraIngestionPayload::issue).toList();
        issueWriter.write(issueEntities);
        log.info("saved {} issues", events.size());

        List<CommentEntity> commentEntities = events.stream().flatMap(event -> event.comments().stream()).toList();
        commentWriter.writeComments(commentEntities);
        log.info("saved {} comments", commentEntities.size());

        List<LinkedIssueEntity> linkedIssueEntities = events.stream().flatMap(event -> event.linkedIssues().stream()).toList();
        linkedIssueWriter.write(linkedIssueEntities);
        log.info("saved {} linked issues", linkedIssueEntities.size());
    }
}