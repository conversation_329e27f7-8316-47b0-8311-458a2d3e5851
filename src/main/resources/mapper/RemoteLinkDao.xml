<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sayweee.datasync.dao.RemoteLinkDao">

    <update id="createTempTableLike">
        CREATE TABLE ${tempTable} as select * from ${targetTable} where 1=0
    </update>

    <insert id="batchInsert">
        INSERT INTO ${tableName} (
            id,
            issue_id,
            remote_link,
            url,
            title
        ) VALUES
        <foreach collection="remoteLinks" item="item" separator=",">
            (
                #{item.id},
                #{item.issueId},
                #{item.remoteLink},
                #{item.url},
                #{item.title}
            )
        </foreach>
    </insert>

    <delete id="deleteFromTargetUsingTemp">
        DELETE FROM ${targetTable}
        WHERE id IN (SELECT id FROM ${tempTable})
    </delete>

    <insert id="insertFromTempToTarget">
        INSERT INTO ${targetTable} (
            id,
            issue_id,
            remote_link,
            url,
            title
        )
        SELECT
            id,
            issue_id,
            remote_link,
            url,
            title
        FROM ${tempTable}
    </insert>

    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

</mapper>
