package com.sayweee.datasync.common.constants;

/**
 * 同步相关常量定义
 */
public final class SyncConstants {
    
    private SyncConstants() {
        // 防止实例化
    }
    
    /**
     * API 路径常量
     */
    public static final class ApiPaths {
        public static final String API_V1_BASE = "/api/v1";
        public static final String JIRA_SYNC_BASE = API_V1_BASE + "/jira";
        public static final String SYNC_ENDPOINT = JIRA_SYNC_BASE + "/sync";
        public static final String TASK_STATUS_ENDPOINT = JIRA_SYNC_BASE + "/sync/{taskId}/status";
        
        private ApiPaths() {}
    }
    
    /**
     * 默认配置常量
     */
    public static final class Defaults {
        public static final int DEFAULT_BATCH_SIZE = 100;
        public static final int DEFAULT_REDSHIFT_BATCH_SIZE = 1000;
        public static final int DEFAULT_THREAD_POOL_SIZE = 5;
        public static final long DEFAULT_TASK_TIMEOUT_MS = 3600000L; // 1小时
        public static final int DEFAULT_RETRY_MAX_ATTEMPTS = 3;
        public static final long DEFAULT_RETRY_DELAY_MS = 1000L;
        
        private Defaults() {}
    }
    
    /**
     * 数据库相关常量
     */
    public static final class Database {
        public static final String SYNC_METADATA_TABLE = "sync_metadata";
        public static final String JIRA_ISSUES_TABLE = "weee_jira_new.issue";
        public static final String JIRA_LINKED_ISSUES_TABLE = "weee_jira_new.issue_linked_issues";
        public static final String JIRA_REMOTE_LINKS_TABLE = "weee_jira_new.issue_remote_links";
        public static final String JIRA_WORKLOGS_TABLE = "weee_jira_new.issue_timelog";
        public static final String JIRA_PROJECTS_TABLE = "jira_projects";
        public static final String JIRA_USERS_TABLE = "jira_users";

        private Database() {}
    }
    
    /**
     * 日期时间格式常量
     */
    public static final class DateTimeFormats {
        public static final String ISO_DATE_FORMAT = "yyyy-MM-dd";
        public static final String ISO_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";
        public static final String JIRA_DATETIME_FORMAT = "yyyy-MM-dd HH:mm";
        public static final String ISO_ZULU_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
        
        private DateTimeFormats() {}
    }
    
    /**
     * 错误代码常量
     */
    public static final class ErrorCodes {
        public static final String VALIDATION_ERROR = "VALIDATION_ERROR";
        public static final String JIRA_API_ERROR = "JIRA_API_ERROR";
        public static final String DATABASE_ERROR = "DATABASE_ERROR";
        public static final String TASK_NOT_FOUND = "TASK_NOT_FOUND";
        public static final String TASK_TIMEOUT = "TASK_TIMEOUT";
        public static final String CONFIGURATION_ERROR = "CONFIGURATION_ERROR";
        
        private ErrorCodes() {}
    }
}