//package com.sayweee.datasync.service.jira;
//
//import com.sayweee.datasync.fetcher.jira.WorklogFetcher;
//import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
//import com.sayweee.datasync.model.entity.WorklogEntity;
//import com.sayweee.datasync.writer.jira.WorklogWriter;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.cloud.stream.function.StreamBridge;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.stream.Stream;
//
//@Service
//@Slf4j
//@RequiredArgsConstructor
//public class WorklogService {
//    private static final String BINDING_NAME = "jira-worklog-events-out-0";
//    private final StreamBridge streamBridge;
//    private final WorklogWriter worklogWriter;
//    private final WorklogFetcher worklogFetcher;
//
//    /**
//     * 处理并获取 Worklog 数据
//     *
//     * @param events JIRA 事件列表
//     */
//    public void processAndFetchWorklog(List<JiraIngestionPayload> events) {
//        if (events == null || events.isEmpty()) {
//            log.info("No events to process for worklogs");
//            return;
//        }
//
//        String taskId = "worklog-fetch-" + System.currentTimeMillis();
//        AtomicInteger total = new AtomicInteger(0);
//
//        log.info("Task [{}] - Starting to process {} events for worklogs", taskId, events.size());
//
//        try {
//            Stream<WorklogEntity> worklogStream = worklogFetcher.fetch(taskId, events);
//
//            worklogStream.forEach(worklogEntity -> {
//                streamBridge.send(BINDING_NAME, worklogEntity);
//                total.incrementAndGet();
//            });
//
//            log.info("Task [{}] - Successfully processed {} worklogs", taskId, total.get());
//        } catch (Exception e) {
//            log.error("Task [{}] - Failed to process worklogs", taskId, e);
//            throw new RuntimeException("Failed to process worklogs for task: " + taskId, e);
//        }
//    }
//
//    public void processAndSaveWorklog(List<WorklogEntity> events) {
//        if (events == null || events.isEmpty()) {
//            log.info("No worklogs to process");
//            return;
//        }
//
//        try {
//            worklogWriter.writeWorklogs(events);
//            log.info("Successfully processed {} worklogs", events.size());
//        } catch (Exception e) {
//            log.error("Failed to process worklogs", e);
//            throw e;
//        }
//    }
//}