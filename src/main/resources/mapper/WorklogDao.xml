<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sayweee.datasync.dao.WorklogDao">

    <update id="createTempTableLike">
        CREATE TABLE ${tempTable} as select * from ${targetTable} where 1=0
    </update>

    <insert id="batchInsert">
        INSERT INTO ${tableName} (
            id,
            created,
            issueid,
            started,
            updated,
            timespent,
            timespentseconds,
            "comment",
            userid,
            username
        ) VALUES
        <foreach collection="worklogs" item="item" separator=",">
            (
                #{item.id},
                #{item.created},
                #{item.issueId},
                #{item.started},
                #{item.updated},
                #{item.timeSpent},
                #{item.timeSpentSeconds},
                #{item.comment},
                #{item.userId},
                #{item.username}
            )
        </foreach>
    </insert>

    <delete id="deleteFromTargetUsingTemp">
        DELETE FROM ${targetTable}
        WHERE id IN (SELECT id FROM ${tempTable})
    </delete>

    <insert id="insertFromTempToTarget">
        INSERT INTO ${targetTable} (
            id,
            created,
            issueid,
            started,
            updated,
            timespent,
            timespentseconds,
            "comment",
            userid,
            username
        )
        SELECT
            id,
            created,
            issueid,
            started,
            updated,
            timespent,
            timespentseconds,
            "comment",
            userid,
            username
        FROM ${tempTable}
    </insert>

    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

</mapper>