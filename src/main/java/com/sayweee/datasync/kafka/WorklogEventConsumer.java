//package com.sayweee.datasync.kafka;
//
//import com.sayweee.datasync.model.entity.WorklogEntity;
//import com.sayweee.datasync.service.jira.WorklogService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.kafka.support.Acknowledgment;
//import org.springframework.kafka.support.KafkaHeaders;
//import org.springframework.messaging.Message;
//
//import java.util.List;
//import java.util.function.Consumer;
//
//@Configuration
//@Slf4j
//@RequiredArgsConstructor
//public class WorklogEventConsumer {
//    private final WorklogService worklogService;
//
//    @Bean
//    public Consumer<Message<List<WorklogEntity>>> consumeAndSaveWorklog() {
//        return message -> {
//            var events = message.getPayload();
//            if (events.isEmpty()) {
//                return;
//            }
//            Acknowledgment acknowledgment = message.getHeaders().get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment.class);
//
//            worklogService.processAndSaveWorklog(events);
//            if (acknowledgment != null) {
//                acknowledgment.acknowledge();
//            }
//            log.info("Received a batch of {} worklog events to process.", events.size());
//        };
//    }
//}