//package com.sayweee.datasync.kafka;
//
//import com.sayweee.datasync.model.entity.RemoteLinkEntity;
//import com.sayweee.datasync.service.jira.RemoteLinkService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.kafka.support.Acknowledgment;
//import org.springframework.kafka.support.KafkaHeaders;
//import org.springframework.messaging.Message;
//
//import java.util.List;
//import java.util.function.Consumer;
//
//@Configuration
//@Slf4j
//@RequiredArgsConstructor
//public class RemoteLinkEventConsumer {
//    private final RemoteLinkService remoteLinkService;
//    @Bean
//    public Consumer<Message<List<RemoteLinkEntity>>> consumeAndSaveRemoteLink() {
//        return message -> {
//            var events = message.getPayload();
//            if (events.isEmpty()) {
//                return;
//            }
//            Acknowledgment acknowledgment = message.getHeaders().get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment.class);
//
//            remoteLinkService.processAndSaveRemoteLink(events);
//            if (acknowledgment != null) {
//                acknowledgment.acknowledge();
//            }
//            log.info("Received a batch of {} events to process.", events.size());
//        };
//    }
//}
