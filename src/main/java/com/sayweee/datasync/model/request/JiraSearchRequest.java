package com.sayweee.datasync.model.request;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Set;

/**
 * JIRA搜索请求对象
 * 用于封装JIRA API搜索参数
 */
@Data
@Builder
@ToString
public class JiraSearchRequest {
    
    /**
     * JQL查询语句
     */
    private String jql;
    
    /**
     * 需要返回的字段列表
     * 如果为空或null，将返回默认字段
     */
    private Set<String> fields;
    
    /**
     * 是否按字段键值返回
     * 默认为true
     */
    @Builder.Default
    private Boolean fieldsByKeys = true;
    
    /**
     * 最大结果数量
     * 默认为50
     */
    @Builder.Default
    private Integer maxResults = 50;
    
    /**
     * 分页令牌
     * 用于获取下一页结果
     */
    private String nextPageToken;
    
    /**
     * 扩展信息
     * 指定需要扩展的字段信息
     */
    @Builder.Default
    private String expand = "names,schema,operations,editmeta,changelog,renderedFields";
    
    /**
     * 属性列表
     * 指定需要返回的属性
     */
    @Builder.Default
    private List<String> properties = List.of("*all");
    
    /**
     * 需要协调的Issue ID列表
     * 用于Issue协调功能
     */
    private List<Integer> reconcileIssues;
    
    /**
     * 创建默认搜索请求
     *
     * @param jql JQL查询语句
     * @return 默认配置的搜索请求
     */
    public static JiraSearchRequest defaultRequest(String jql) {
        return JiraSearchRequest.builder()
                .jql(jql)
                .build();
    }
    
    /**
     * 创建带字段的搜索请求
     *
     * @param jql JQL查询语句
     * @param fields 需要返回的字段
     * @return 配置了字段的搜索请求
     */
    public static JiraSearchRequest withFields(String jql, Set<String> fields) {
        return JiraSearchRequest.builder()
                .jql(jql)
                .fields(fields)
                .build();
    }
    
    /**
     * 创建分页搜索请求
     *
     * @param jql JQL查询语句
     * @param maxResults 最大结果数
     * @param nextPageToken 分页令牌
     * @return 配置了分页的搜索请求
     */
    public static JiraSearchRequest withPagination(String jql, Integer maxResults, String nextPageToken) {
        return JiraSearchRequest.builder()
                .jql(jql)
                .maxResults(maxResults)
                .nextPageToken(nextPageToken)
                .build();
    }
    
    /**
     * 验证请求参数
     *
     * @return 如果参数有效返回true，否则返回false
     */
    public boolean isValid() {
        return jql != null && !jql.trim().isEmpty();
    }
    
    /**
     * 应用默认值
     * 为空的字段设置默认值
     */
    public void applyDefaults() {
        if (fieldsByKeys == null) {
            fieldsByKeys = true;
        }
        
        if (maxResults == null || maxResults <= 0) {
            maxResults = 50;
        }
        
        if (expand == null || expand.trim().isEmpty()) {
            expand = "names,schema,operations,editmeta,changelog,renderedFields";
        }
        
        if (properties == null || properties.isEmpty()) {
            properties = List.of("*all");
        }
    }
}
