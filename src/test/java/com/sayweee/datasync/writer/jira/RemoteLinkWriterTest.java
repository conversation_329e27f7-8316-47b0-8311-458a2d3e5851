package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.dao.RemoteLinkDao;
import com.sayweee.datasync.model.entity.RemoteLinkEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RemoteLinkWriterTest {

    @Mock
    private RemoteLinkDao remoteLinkDao;

    @InjectMocks
    private RemoteLinkWriter remoteLinkWriter;

    private List<RemoteLinkEntity> testRemoteLinks;

    @BeforeEach
    void setUp() {
        testRemoteLinks = Arrays.asList(
                new RemoteLinkEntity(1L, 12345, "remote-link-1", "https://example.com/link1", "Test Link 1"),
                new RemoteLinkEntity(2L, 12346, "remote-link-2", "https://example.com/link2", "Test Link 2")
        );
    }

    @Test
    void testWriteRemoteLinks_WithNullList_ShouldReturnEarly() {
        // When
        assertDoesNotThrow(() -> remoteLinkWriter.writeRemoteLinks(null));

        // Then
        verifyNoInteractions(remoteLinkDao);
    }

    @Test
    void testWriteRemoteLinks_WithEmptyList_ShouldReturnEarly() {
        // When
        assertDoesNotThrow(() -> remoteLinkWriter.writeRemoteLinks(Collections.emptyList()));

        // Then
        verifyNoInteractions(remoteLinkDao);
    }

    @Test
    void testWriteRemoteLinks_WithValidData_ShouldProcessSuccessfully() {
        // Given
        when(remoteLinkDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(2);
        when(remoteLinkDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(3);

        // When
        assertDoesNotThrow(() -> remoteLinkWriter.writeRemoteLinks(testRemoteLinks));

        // Then
        verify(remoteLinkDao).createTempTableLike(anyString(), eq("weee_jira_new.issue_remote_links"));
        verify(remoteLinkDao).batchInsert(anyString(), eq(testRemoteLinks));
        verify(remoteLinkDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(remoteLinkDao).insertFromTempToTarget(anyString(), anyString());
        verify(remoteLinkDao).dropTable(anyString());
    }

    @Test
    void testWriteRemoteLinks_WhenCreateTempTableFails_ShouldThrowException() {
        // Given
        doThrow(new RuntimeException("Database error")).when(remoteLinkDao)
                .createTempTableLike(anyString(), anyString());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> remoteLinkWriter.writeRemoteLinks(testRemoteLinks));
        
        assertTrue(exception.getMessage().contains("Failed to write remote links to database"));
    }

    @Test
    void testWriteRemoteLinks_WhenBatchInsertFails_ShouldThrowExceptionAndCleanup() {
        // Given
        doThrow(new RuntimeException("Insert error")).when(remoteLinkDao)
                .batchInsert(anyString(), anyList());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> remoteLinkWriter.writeRemoteLinks(testRemoteLinks));
        
        assertTrue(exception.getMessage().contains("Failed to write remote links to database"));
        
        // Verify cleanup is attempted
        verify(remoteLinkDao).dropTable(anyString());
    }

    @Test
    void testWriteRemoteLinks_WhenUpsertFails_ShouldThrowExceptionAndCleanup() {
        // Given
        doThrow(new RuntimeException("Upsert error")).when(remoteLinkDao)
                .deleteFromTargetUsingTemp(anyString(), anyString());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> remoteLinkWriter.writeRemoteLinks(testRemoteLinks));
        
        assertTrue(exception.getMessage().contains("Failed to write remote links to database"));
        
        // Verify cleanup is attempted
        verify(remoteLinkDao).dropTable(anyString());
    }

    @Test
    void testWriteRemoteLinks_WhenDropTableFails_ShouldNotThrowException() {
        // Given
        when(remoteLinkDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(2);
        when(remoteLinkDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(3);
        doThrow(new RuntimeException("Drop table error")).when(remoteLinkDao)
                .dropTable(anyString());

        // When & Then
        assertDoesNotThrow(() -> remoteLinkWriter.writeRemoteLinks(testRemoteLinks));
        
        // Verify all operations were attempted
        verify(remoteLinkDao).createTempTableLike(anyString(), anyString());
        verify(remoteLinkDao).batchInsert(anyString(), eq(testRemoteLinks));
        verify(remoteLinkDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(remoteLinkDao).insertFromTempToTarget(anyString(), anyString());
        verify(remoteLinkDao).dropTable(anyString());
    }

    @Test
    void testWriteRemoteLinks_WithLargeDataset_ShouldProcessSuccessfully() {
        // Given
        List<RemoteLinkEntity> largeDataset = createLargeTestDataset(1000);
        when(remoteLinkDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(500);
        when(remoteLinkDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(1000);

        // When
        assertDoesNotThrow(() -> remoteLinkWriter.writeRemoteLinks(largeDataset));

        // Then
        verify(remoteLinkDao).createTempTableLike(anyString(), eq("weee_jira_new.issue_remote_links"));
        verify(remoteLinkDao).batchInsert(anyString(), eq(largeDataset));
        verify(remoteLinkDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(remoteLinkDao).insertFromTempToTarget(anyString(), anyString());
        verify(remoteLinkDao).dropTable(anyString());
    }

    private List<RemoteLinkEntity> createLargeTestDataset(int size) {
        return java.util.stream.IntStream.range(0, size)
                .mapToObj(i -> new RemoteLinkEntity(
                        (long) i,
                        12345 + i,
                        "remote-link-" + i,
                        "https://example.com/link" + i,
                        "Test Link " + i
                ))
                .toList();
    }
}
