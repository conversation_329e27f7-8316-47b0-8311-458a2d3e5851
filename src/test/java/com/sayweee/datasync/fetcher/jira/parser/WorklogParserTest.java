package com.sayweee.datasync.fetcher.jira.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sayweee.datasync.model.entity.WorklogEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class WorklogParserTest {

    private WorklogParser worklogParser;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        worklogParser = new WorklogParser();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testParse_WithValidWorklogData_ShouldReturnParsedEntities() throws Exception {
        // Given
        String jsonData = """
                {
                    "worklogs": [
                        {
                            "id": "100",
                            "created": "2023-01-01T10:00:00.000+0000",
                            "started": "2023-01-01T09:00:00.000+0000",
                            "updated": "2023-01-01T11:00:00.000+0000",
                            "timeSpent": "1h",
                            "timeSpentSeconds": 3600,
                            "comment": "Working on feature",
                            "author": {
                                "accountId": "user123",
                                "displayName": "John Doe"
                            }
                        },
                        {
                            "id": "101",
                            "created": "2023-01-02T10:00:00.000+0000",
                            "started": "2023-01-02T09:00:00.000+0000",
                            "updated": "2023-01-02T11:00:00.000+0000",
                            "timeSpent": "2h",
                            "timeSpentSeconds": 7200,
                            "comment": "Bug fixing",
                            "author": {
                                "accountId": "user456",
                                "displayName": "Jane Smith"
                            }
                        }
                    ]
                }
                """;
        JsonNode jsonNode = objectMapper.readTree(jsonData);
        Integer issueId = 12345;

        // When
        List<WorklogEntity> result = worklogParser.parse(jsonNode, issueId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        WorklogEntity firstWorklog = result.get(0);
        assertEquals(100, firstWorklog.getId());
        assertEquals(issueId, firstWorklog.getIssueId());
        assertEquals("1h", firstWorklog.getTimeSpent());
        assertEquals(3600, firstWorklog.getTimeSpentSeconds());
        assertEquals("Working on feature", firstWorklog.getComment());
        assertEquals("user123", firstWorklog.getUserId());
        assertEquals("John Doe", firstWorklog.getUsername());

        WorklogEntity secondWorklog = result.get(1);
        assertEquals(101, secondWorklog.getId());
        assertEquals(issueId, secondWorklog.getIssueId());
        assertEquals("2h", secondWorklog.getTimeSpent());
        assertEquals(7200, secondWorklog.getTimeSpentSeconds());
        assertEquals("Bug fixing", secondWorklog.getComment());
        assertEquals("user456", secondWorklog.getUserId());
        assertEquals("Jane Smith", secondWorklog.getUsername());
    }

    @Test
    void testParse_WithNullNode_ShouldReturnEmptyList() {
        // When
        List<WorklogEntity> result = worklogParser.parse(null, 12345);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParse_WithEmptyWorklogsArray_ShouldReturnEmptyList() throws Exception {
        // Given
        String jsonData = """
                {
                    "worklogs": []
                }
                """;
        JsonNode jsonNode = objectMapper.readTree(jsonData);
        Integer issueId = 12345;

        // When
        List<WorklogEntity> result = worklogParser.parse(jsonNode, issueId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParse_WithADFComment_ShouldExtractText() throws Exception {
        // Given
        String jsonData = """
                {
                    "worklogs": [
                        {
                            "id": "100",
                            "created": "2023-01-01T10:00:00.000+0000",
                            "started": "2023-01-01T09:00:00.000+0000",
                            "updated": "2023-01-01T11:00:00.000+0000",
                            "timeSpent": "1h",
                            "timeSpentSeconds": 3600,
                            "comment": {
                                "type": "doc",
                                "version": 1,
                                "content": [
                                    {
                                        "type": "paragraph",
                                        "content": [
                                            {
                                                "type": "text",
                                                "text": "Working on feature implementation"
                                            }
                                        ]
                                    }
                                ]
                            },
                            "author": {
                                "accountId": "user123",
                                "displayName": "John Doe"
                            }
                        }
                    ]
                }
                """;
        JsonNode jsonNode = objectMapper.readTree(jsonData);
        Integer issueId = 12345;

        // When
        List<WorklogEntity> result = worklogParser.parse(jsonNode, issueId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        WorklogEntity worklog = result.get(0);
        assertEquals("Working on feature implementation", worklog.getComment());
    }
}