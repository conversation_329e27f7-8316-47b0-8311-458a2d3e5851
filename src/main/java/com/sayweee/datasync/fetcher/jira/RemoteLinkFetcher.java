package com.sayweee.datasync.fetcher.jira;

import com.fasterxml.jackson.databind.JsonNode;
import com.sayweee.datasync.fetcher.jira.client.JiraHttpClient;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.fetcher.jira.parser.RemoteLinkParser;
import com.sayweee.datasync.model.entity.RemoteLinkEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
@Component
@RequiredArgsConstructor
public class RemoteLinkFetcher {
    private final JiraHttpClient httpClient;
    private final RemoteLinkParser remoteLinkParser;

    private record RemoteLinkPageState(List<RemoteLinkEntity> currentPageRemoteLinks, boolean hasMore, int currentIndex) {
    }

    private record RemoteLinkPageResult(List<RemoteLinkEntity> remoteLinks, boolean hasMore) {
    }

    /**
     * 获取指定 Issues 的远程链接数据流
     *
     * @param taskId 任务ID
     * @param events JIRA 事件列表
     * @return 远程链接实体流
     */
    public Stream<RemoteLinkEntity> fetch(String taskId, List<JiraIngestionPayload> events) {
        var issueIdList = events
                .stream()
                .map(JiraIngestionPayload::issue)
                .map(issue -> issue.id().toString())
                .toList();

        log.info("Task [{}] - Starting to fetch JIRA remote links for {} issues.", taskId, issueIdList.size());

        try {
            // 1. 获取初始页面数据以启动流
            RemoteLinkPageResult initialResult = fetchRemoteLinksForIssues(taskId, issueIdList, 0);
            log.info("Task [{}] - Initial remote links fetch complete.", taskId);

            // 2. 为流迭代器创建初始状态
            RemoteLinkPageState initialState = new RemoteLinkPageState(
                    initialResult.remoteLinks(),
                    initialResult.hasMore(),
                    0
            );

            return Stream.iterate(
                            initialState,
                            state -> !state.currentPageRemoteLinks().isEmpty(),
                            currentState -> {
                                if (!currentState.hasMore()) {
                                    log.info("Task [{}] - Reached the last issue, no more remote links to fetch.", taskId);
                                    return new RemoteLinkPageState(Collections.emptyList(), false, currentState.currentIndex());
                                }

                                int nextIndex = currentState.currentIndex() + 1;
                                log.debug("Task [{}] - Fetching remote links for next issue. Index: {}", taskId, nextIndex);

                                try {
                                    RemoteLinkPageResult nextResult = fetchRemoteLinksForIssues(taskId, issueIdList, nextIndex);
                                    return new RemoteLinkPageState(
                                            nextResult.remoteLinks(),
                                            nextResult.hasMore() && nextIndex < issueIdList.size() - 1,
                                            nextIndex
                                    );
                                } catch (IOException | InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                    throw new RuntimeException("Task [" + taskId + "] - Failed to fetch remote links for subsequent issue", e);
                                }
                            }
                    )
                    .flatMap(state -> state.currentPageRemoteLinks().stream());

        } catch (Exception e) {
            log.error("Task [{}] - Failed to fetch JIRA remote links.", taskId, e);
            throw new RuntimeException("Task [" + taskId + "] - Failed to fetch JIRA remote links", e);
        }
    }

    /**
     * 获取指定 Issues 的远程链接数据
     *
     * @param taskId 任务ID
     * @param issueIdList Issue ID 列表
     * @param currentIndex 当前处理的 Issue 索引
     * @return 远程链接页面结果
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    private RemoteLinkPageResult fetchRemoteLinksForIssues(String taskId, List<String> issueIdList, int currentIndex)
            throws IOException, InterruptedException {


        if (currentIndex >= issueIdList.size()) {
            return new RemoteLinkPageResult(Collections.emptyList(), false);
        }

        String issueId = issueIdList.get(currentIndex);
        if("294230".equals(issueId)) {
            log.info("issueId is 294230");
        }
        String endpoint = "/rest/api/3/issue/" + issueId + "/remotelink";

        log.debug("Task [{}] - Fetching remote links for issue: {}", taskId, issueId);

        JsonNode responseNode = httpClient.sendGetRequest(taskId, endpoint);

        List<RemoteLinkEntity> remoteLinks = new ArrayList<>();
        if (responseNode != null && responseNode.isArray()) {
            try {
                remoteLinks.addAll(remoteLinkParser.parse(responseNode, Integer.valueOf(issueId)));
            } catch (Exception e) {
                log.warn("Task [{}] - Failed to parse remote links for issue {}: {}", taskId, issueId, e.getMessage(), e);
            }
        }

        boolean hasMore = currentIndex < issueIdList.size() - 1;
        log.debug("Task [{}] - Fetched {} remote links for issue: {}, hasMore: {}", taskId, remoteLinks.size(), issueId, hasMore);

        return new RemoteLinkPageResult(remoteLinks, hasMore);
    }
}