<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sayweee.datasync.dao.ChangeLogDao">

    <update id="createTempTableLike">
        CREATE TABLE ${tempTable} as select * from ${targetTable} where 1=0
    </update>

    <insert id="batchInsert">
        INSERT INTO ${tableName} (
            changeid,
            issue_id,
            userid,
            username,
            created,
            field,
            from_value,
            from_string,
            to_value,
            to_string,
            in_date
        ) VALUES
        <foreach collection="changeLogs" item="item" separator=",">
            (
                #{item.changeId},
                #{item.issueId},
                #{item.userId},
                #{item.username},
                #{item.created},
                #{item.field},
                #{item.fromValue},
                #{item.fromString},
                #{item.toValue},
                #{item.toString},
                SYSDATE
            )
        </foreach>
    </insert>

    <delete id="deleteFromTargetUsingTemp">
        DELETE FROM ${targetTable}
        WHERE changeid IN (SELECT changeid FROM ${tempTable})
    </delete>

    <insert id="insertFromTempToTarget">
        INSERT INTO ${targetTable} (
            changeid,
            issue_id,
            userid,
            username,
            created,
            field,
            from_value,
            from_string,
            to_value,
            to_string,
            in_date
        )
        SELECT
            changeid,
            issue_id,
            userid,
            username,
            created,
            field,
            from_value,
            from_string,
            to_value,
            to_string,
            in_date
        FROM ${tempTable}
    </insert>

    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

</mapper>
