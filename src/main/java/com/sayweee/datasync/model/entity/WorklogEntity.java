package com.sayweee.datasync.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;

/**
 * JIRA Issue Worklog 实体类
 * 对应数据库表: weee_jira_new.issue_timelog
 */
@Getter
@Setter
public class WorklogEntity {

    /**
     * Worklog ID
     */
    private Integer id;

    /**
     * 创建时间
     */
    private OffsetDateTime created;

    /**
     * Issue ID
     */
    private Integer issueId;

    /**
     * 工作开始时间
     */
    private OffsetDateTime started;

    /**
     * 更新时间
     */
    private OffsetDateTime updated;

    /**
     * 时间花费描述
     */
    private String timeSpent;

    /**
     * 时间花费秒数
     */
    private Integer timeSpentSeconds;

    /**
     * 工作日志评论
     */
    private String comment;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 入库时间
     */
    private LocalDateTime inDate;

    /**
     * 默认构造函数
     */
    public WorklogEntity() {
    }

    /**
     * 全参数构造函数
     */
    public WorklogEntity(Integer id, OffsetDateTime created, Integer issueId, OffsetDateTime started, 
                        OffsetDateTime updated, String timeSpent, Integer timeSpentSeconds, 
                        String comment, String userId, String username) {
        this.id = id;
        this.created = created;
        this.issueId = issueId;
        this.started = started;
        this.updated = updated;
        this.timeSpent = timeSpent;
        this.timeSpentSeconds = timeSpentSeconds;
        this.comment = comment;
        this.userId = userId;
        this.username = username;
    }

    @Override
    public String toString() {
        return "WorklogEntity{" +
                "id=" + id +
                ", created=" + created +
                ", issueId=" + issueId +
                ", started=" + started +
                ", updated=" + updated +
                ", timeSpent='" + timeSpent + '\'' +
                ", timeSpentSeconds=" + timeSpentSeconds +
                ", comment='" + comment + '\'' +
                ", userId='" + userId + '\'' +
                ", username='" + username + '\'' +
                ", inDate=" + inDate +
                '}';
    }
}